/**
 * Audio streaming utility - Receive only version
 * Optimized for ultra-smooth continuous playback
 */

// Variables for audio context and processing
let audioContext: AudioContext | null = null;
let gainNode: GainNode | null = null;
let inputGainNode: GainNode | null = null; // Separate gain node for input (microphone)
let isPlaying = false;
let currentSource: AudioBufferSourceNode | null = null;

// Lazy browser detection functions (only execute when needed)
function getIsSafari(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
}

function getIsIOS(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

function getIsMobile(): boolean {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false;
  }
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// Mobile and Safari-specific audio configuration (computed lazily)
function getSafariConfig() {
  const isSafari = getIsSafari();
  const isIOS = getIsIOS();
  const isMobile = getIsMobile();

  // Higher gain values for all platforms to ensure loud audio everywhere
  let outputGain = 2.5; // Increased base volume for all platforms
  let inputGain = 2.0;  // Increased base input gain for all platforms

  if (isIOS || isSafari) {
    // iOS Safari needs significant volume boost
    outputGain = 3.5; // Increased from 3.0 to 3.5 for even louder audio
    inputGain = 2.5;
  } else if (isMobile) {
    // Other mobile browsers also need volume boost
    outputGain = 3.0; // Increased from 2.5 to 3.0
    inputGain = 2.0;
  }
  // Desktop/Windows now gets 2.5x instead of 1.0x for louder audio

  return {
    // Output (playback) gain multiplier for all platforms (now boosted)
    OUTPUT_GAIN: outputGain,
    // Input (microphone) gain multiplier for all platforms (now boosted)
    INPUT_GAIN: inputGain,
    // Keep Azure's required sample rate for playback
    PLAYBACK_SAMPLE_RATE: 24000,
    // Use higher sample rate for microphone input for better quality
    INPUT_SAMPLE_RATE: isSafari ? 44100 : 48000
  };
}
let lastEndTime = 0;
let socket: WebSocket | null = null;
let isSocketConnected = false;
let lastSentAudioTimestamp = 0;
let lastReceivedAudioTimestamp = 0;
let mediaStream: MediaStream | null = null;
let mediaStreamSource: MediaStreamAudioSourceNode | null = null;
let scriptProcessor: ScriptProcessorNode | null = null;
let audioWorkletNode: AudioWorkletNode | null = null;
let isRecording = false;
let isAssistantSpeaking = false;
let audioQueue: AudioBuffer[] = [];
let lastMessageType = '';
let isWebSocketPaused = false; // Flag to track if WebSocket is paused
let pingInterval: NodeJS.Timeout | null = null; // Interval for keepalive pings

// WebSocket safety variables
let isUnmounting = false; // Flag to track if component is unmounting
let isReconnectionMode = false; // Flag to indicate we're in reconnection mode

// Define interruption indicators
const INTERRUPTION_INDICATORS = [
  "conversation.item.input_audio_transcription.started", // User started speaking
  "response.interrupted",                                // Response was interrupted
  "conversation.item.input_audio_transcription.completed" // User finished speaking
];

// Define response completion indicators
const RESPONSE_COMPLETION_INDICATORS = [
  "response.done",                    // Response is complete
  "response.audio.done",             // Audio response is complete
  "response.output_audio.done"       // Output audio is complete
];

/**
 * Reset all global state for fresh screen transitions
 * This fixes the issue where audio doesn't work after navigating between screens
 */
export function resetGlobalAudioState(): void {
  console.log(`Resetting global audio state for fresh screen transition (reconnectionMode: ${isReconnectionMode})`);

  // Reset all problematic state variables
  isWebSocketPaused = false;
  isUnmounting = false;
  lastMessageType = '';
  isPlaying = false;
  isAssistantSpeaking = false;
  lastEndTime = 0;

  // Audio state reset - microphone enabled by default
  console.log('Audio state reset - microphone will be enabled by default');

  // Clear audio queue
  audioQueue = [];

  // Reset current source
  if (currentSource) {
    try {
      currentSource.stop();
      currentSource.disconnect();
    } catch (e) {
      // Ignore errors if already stopped
    }
    currentSource = null;
  }

  // Reset timestamps
  lastSentAudioTimestamp = 0;
  lastReceivedAudioTimestamp = 0;

  console.log('Global audio state reset completed');
}

// Get the current audio context
export function getAudioContext(): AudioContext | null {
  return audioContext;
}

// Get the current WebSocket paused state
export function isWebSocketPausedState(): boolean {
  return isWebSocketPaused;
}

// Set the WebSocket paused state
export function setWebSocketPausedState(paused: boolean): void {
  isWebSocketPaused = paused;
  console.log(`WebSocket paused state set to: ${paused}`);

  // Instead of clearing the queue, just mute the audio
  if (gainNode && audioContext) {
    if (paused) {
      // Mute audio by setting gain to 0
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      console.log('Audio muted (gain set to 0)');
    } else {
      // Unmute audio by setting gain back to platform-specific value
      const safariConfig = getSafariConfig();
      gainNode.gain.setValueAtTime(safariConfig.OUTPUT_GAIN, audioContext.currentTime);
      console.log(`Audio unmuted (gain restored to ${safariConfig.OUTPUT_GAIN}x)`);
    }
  }
}

// Initialize the audio context for playback - modified to require user gesture
export async function initAudioContext(fromUserGesture = false): Promise<AudioContext | null> {
  // If we already have an audio context, just ensure it's running
  if (audioContext) {
    if (audioContext.state === 'suspended' && fromUserGesture) {
      try {
        await audioContext.resume();
        console.log('Audio context resumed from suspended state');
      } catch (resumeErr) {
        console.error('Error resuming audio context:', resumeErr);
      }
    }
    return audioContext;
  }

  // Only create a new AudioContext if this is from a user gesture or we're just preparing
  try {
    const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;

    if (!AudioContextClass) {
      console.error('AudioContext not supported in this browser');
      return null;
    }

    // Get Safari configuration
    const safariConfig = getSafariConfig();

    // Create the context with Azure's required sample rate for playback
    audioContext = new AudioContextClass({
      sampleRate: safariConfig.PLAYBACK_SAMPLE_RATE  // Keep Azure's 24kHz for correct playback speed
    });

    // Create output gain node with Safari-specific gain adjustment
    gainNode = audioContext.createGain();
    gainNode.gain.value = safariConfig.OUTPUT_GAIN;
    gainNode.connect(audioContext.destination);

    // Create input gain node for microphone with Safari-specific gain adjustment
    inputGainNode = audioContext.createGain();
    inputGainNode.gain.value = safariConfig.INPUT_GAIN;

    console.log('Audio context initialized with sample rate:', audioContext.sampleRate);
    console.log('Mobile/Safari audio config applied:', {
      isSafari: getIsSafari(),
      isIOS: getIsIOS(),
      isMobile: getIsMobile(),
      outputGain: safariConfig.OUTPUT_GAIN,
      inputGain: safariConfig.INPUT_GAIN,
      playbackSampleRate: safariConfig.PLAYBACK_SAMPLE_RATE,
      inputSampleRate: safariConfig.INPUT_SAMPLE_RATE
    });
    
    // Only resume if this was triggered by a user gesture
    if (fromUserGesture && audioContext.state === 'suspended') {
      try {
        await audioContext.resume();
        console.log('Audio context resumed immediately after creation');
      } catch (resumeErr) {
        console.error('Error resuming new audio context:', resumeErr);
      }
    }
  } catch (e) {
    console.error('Error initializing audio context:', e);

    // Log audio context initialization error
    try {
      import('./voiceLogging').then(({ logVoiceMessage }) => {
        logVoiceMessage({
          speaker: 'assistant',
          timestamp: Date.now(),
          details: {
            messageType: 'audio_context_init_error',
            error: `Audio context initialization failed: ${e instanceof Error ? e.message : String(e)}`
          }
        });
      }).catch(err => console.log('Error logging audio context init error:', err));
    } catch (err) {
      console.log('Error with audio context init error logging:', err);
    }

    return null;
  }

  return audioContext;
}

// Play audio from base64 string - completely redesigned for continuous playback
export async function playAudio(base64Data: string): Promise<void> {
  const playbackStartTime = Date.now();

  // Skip if we've detected an interruption but haven't fully processed it yet
  if (INTERRUPTION_INDICATORS.includes(lastMessageType)) {
    console.log('Skipping audio playback due to recent interruption');

    // Log skipped playback
    try {
      import('./voiceLogging').then(({ logVoiceMessage }) => {
        logVoiceMessage({
          speaker: 'assistant',
          timestamp: Date.now(),
          audioMetadata: {
            playbackStartTime,
            audioDataSize: base64Data.length,
            silenceDetected: true
          },
          details: {
            messageType: 'playback_skipped_interruption',
            error: 'Skipped due to interruption'
          }
        });
      }).catch(err => console.log('Error logging skipped playback:', err));
    } catch (err) {
      console.log('Error with playback logging:', err);
    }

    return;
  }

  // Skip if WebSocket is paused
  if (isWebSocketPaused) {
    console.log('Audio processing continues but output is muted');
    // Continue processing - the gain is already set to 0 in setWebSocketPausedState
  }

  if (!audioContext || !gainNode) {
    await initAudioContext();
    if (!audioContext || !gainNode) {
      console.error('Failed to initialize audio context');

      // Log audio context initialization error
      try {
        import('./voiceLogging').then(({ logVoiceMessage }) => {
          logVoiceMessage({
            speaker: 'assistant',
            timestamp: Date.now(),
            audioMetadata: {
              playbackStartTime,
              audioDataSize: base64Data.length,
              silenceDetected: true
            },
            details: {
              messageType: 'playback_error',
              error: 'Failed to initialize audio context'
            }
          });
        }).catch(err => console.log('Error logging audio context error:', err));
      } catch (err) {
        console.log('Error with audio context error logging:', err);
      }

      return;
    }
  }

  try {
    // Set assistant speaking state
    isAssistantSpeaking = true;

    // Assistant speaking - microphone continues to work normally
    console.log('Assistant speaking - microphone remains active');

    // Resume audio context if suspended
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }

    const binary = atob(base64Data);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }

    const pcmData = new Int16Array(bytes.buffer);

    // Create audio buffer from PCM data
    const audioBuffer = await createAudioBuffer(pcmData);
    if (!audioBuffer) return;

    // Add to queue with timestamp for potential filtering
    audioQueue.push(audioBuffer);

    // Skip old audio chunks if they're too old (optimized threshold)
    if (Date.now() - lastReceivedAudioTimestamp > 1000 && audioQueue.length > 1) {
      // Reduced threshold from 2000ms to 1000ms for faster response
      console.log("Skipping old audio chunk");
      return;
    }

    // If we're not currently playing, start playback
    if (!isPlaying) {
      playNextBuffer();
    }

    // Make sure recording continues even while playing audio
    // This is essential for the interruption feature to work
    if (isRecording && mediaStreamSource && scriptProcessor) {
      // Ensure connections are maintained
      try {
        if (!mediaStreamSource.numberOfOutputs) {
          // Reconnecting media stream for interruption support
          if (inputGainNode) {
            // Reconnect with Safari gain compensation
            mediaStreamSource.connect(inputGainNode);
            inputGainNode.connect(scriptProcessor);
            scriptProcessor.connect(audioContext.destination);
          } else {
            // Fallback to direct connection
            mediaStreamSource.connect(scriptProcessor);
            scriptProcessor.connect(audioContext.destination);
          }
        }
      } catch (err) {
        console.error('Error ensuring recording continues during playback:', err);
      }
    }
  } catch (error) {
    console.error("Error processing audio:", error);

    // Log audio processing error
    try {
      import('./voiceLogging').then(({ logVoiceMessage }) => {
        logVoiceMessage({
          speaker: 'assistant',
          timestamp: Date.now(),
          audioMetadata: {
            playbackStartTime,
            audioDataSize: base64Data.length,
            silenceDetected: true
          },
          details: {
            messageType: 'playback_error',
            error: `Audio processing error: ${error instanceof Error ? error.message : String(error)}`
          }
        });
      }).catch(err => console.log('Error logging audio processing error:', err));
    } catch (err) {
      console.log('Error with audio processing error logging:', err);
    }
  }
}

// Play the next buffer in the queue
function playNextBuffer(): void {
  if (!audioContext || !gainNode) return;

  // If there's nothing in the queue or we've been interrupted, stop playback
  if (audioQueue.length === 0 || INTERRUPTION_INDICATORS.includes(lastMessageType)) {
    isPlaying = false;
    isAssistantSpeaking = false;
    return;
  }

  isPlaying = true;
  const audioBuffer = audioQueue.shift();

  if (!audioBuffer) {
    isPlaying = false;
    return;
  }

  try {
    // Calculate precise timing for gapless playback
    const now = audioContext.currentTime;
    const startTime = Math.max(now, lastEndTime);

    // Create and configure source
    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;

    // Create individual gain node for this buffer for smooth transitions
    const bufferGain = audioContext.createGain();
    bufferGain.gain.value = 1.0;

    // Connect source -> buffer gain -> main gain -> output
    source.connect(bufferGain);
    bufferGain.connect(gainNode);

    // Set up completion callback with playback duration tracking
    source.onended = () => {
      const playbackEndTime = Date.now();
      const requestStartTime = (source as any).requestStartTime || playbackEndTime;
      const audioContextStartTime = (source as any).audioContextStartTime || startTime;

      // Calculate proper timing values (all in milliseconds)
      const actualPlaybackDuration = Math.max(0, playbackEndTime - requestStartTime);
      const expectedDuration = Math.max(0, audioBuffer.duration * 1000); // Convert to ms

      // Calculate latency: time from request to actual audio start
      // AudioContext.currentTime is in seconds, so convert to ms
      const audioStartTimeMs = audioContextStartTime * 1000;
      const playbackLatency = Math.max(0, audioStartTimeMs - requestStartTime);

      // Validate timing values
      const validatedMetadata = {
        playbackStartTime: requestStartTime,
        playbackEndTime,
        playbackLatency: Number.isFinite(playbackLatency) ? Math.round(playbackLatency) : 0,
        actualPlaybackDuration: Number.isFinite(actualPlaybackDuration) ? Math.round(actualPlaybackDuration) : 0,
        duration: Number.isFinite(expectedDuration) ? Math.round(expectedDuration) : 0,
        audioDataSize: (source as any).originalDataSize || 0
      };

      // Log playback completion with enhanced audio diagnostics for iPhone debugging
      try {
        Promise.all([
          import('./voiceLogging'),
          import('./audioAnalysis')
        ]).then(([{ logVoiceMessage }, { analyzeAudioData }]) => {
          // Try to get the original PCM data for analysis
          const originalPCMData = (source as any).originalPCMData;
          let audioAnalysis = null;

          if (originalPCMData) {
            try {
              // Convert Int16Array to Float32Array for analysis
              const float32Data = new Float32Array(originalPCMData.length);
              for (let i = 0; i < originalPCMData.length; i++) {
                float32Data[i] = originalPCMData[i] / 32768.0;
              }
              audioAnalysis = analyzeAudioData(float32Data, 24000, 1);
            } catch (err) {
              console.log('Error analyzing audio for playback completion:', err);
            }
          }

          logVoiceMessage({
            speaker: 'assistant',
            timestamp: playbackEndTime,
            audioMetadata: {
              ...validatedMetadata,
              // Include audio analysis if available
              ...(audioAnalysis ? {
                frequencyHz: audioAnalysis.frequencyHz,
                amplitudeDb: audioAnalysis.amplitudeDb,
                silenceDetected: audioAnalysis.silenceDetected,
                maxAmplitude: audioAnalysis.maxAmplitude,
                peakRMS: audioAnalysis.peakRMS
              } : {
                frequencyHz: 0,
                amplitudeDb: -Infinity,
                silenceDetected: true
              })
            },
            details: {
              messageType: 'playback_completed',
              processingTime: validatedMetadata.actualPlaybackDuration,
              iphoneDebugging: true,
              hasFrequencyData: !!audioAnalysis?.frequencyHz,
              hasAmplitudeData: !!audioAnalysis?.amplitudeDb
            }
          });
        }).catch(err => console.log('Error logging playback completion:', err));
      } catch (err) {
        console.log('Error with playback completion logging:', err);
      }

      currentSource = null;
      playNextBuffer();
    };

    // Store timing info on the source for tracking (all in consistent units)
    (source as any).requestStartTime = Date.now(); // ms
    (source as any).audioContextStartTime = startTime; // seconds (AudioContext time)
    (source as any).originalDataSize = (audioBuffer as any).originalDataSize || 0;
    (source as any).originalPCMData = (audioBuffer as any).originalPCMData; // For iPhone debugging

    // Start playback at precisely calculated time
    source.start(startTime);

    // Update timing for next buffer
    lastEndTime = startTime + audioBuffer.duration;
    currentSource = source;

    // Log for debugging
    console.log(`Playing audio: duration=${audioBuffer.duration.toFixed(3)}s, start=${startTime.toFixed(3)}, end=${lastEndTime.toFixed(3)}`);
  } catch (error) {
    console.error("Error playing audio buffer:", error);

    // Log audio buffer playback error
    try {
      import('./voiceLogging').then(({ logVoiceMessage }) => {
        logVoiceMessage({
          speaker: 'assistant',
          timestamp: Date.now(),
          audioMetadata: {
            audioDataSize: audioBuffer ? audioBuffer.length : 0,
            duration: audioBuffer ? audioBuffer.duration * 1000 : 0,
            silenceDetected: true
          },
          details: {
            messageType: 'playback_buffer_error',
            error: `Audio buffer playback error: ${error instanceof Error ? error.message : String(error)}`
          }
        });
      }).catch(err => console.log('Error logging buffer playback error:', err));
    } catch (err) {
      console.log('Error with buffer playback error logging:', err);
    }

    // Try to continue with the next buffer
    setTimeout(playNextBuffer, 100);
  }
}

// Create an audio buffer from PCM data - optimized for performance and voice quality
async function createAudioBuffer(pcmData: Int16Array): Promise<AudioBuffer | null> {
  if (!audioContext) return null;

  const audioBuffer = audioContext.createBuffer(1, pcmData.length, audioContext.sampleRate);
  const channelData = audioBuffer.getChannelData(0);

  // Debug log for audio buffer creation
  if (getIsSafari()) {
    console.log('Creating audio buffer for Safari:', {
      length: pcmData.length,
      sampleRate: audioContext.sampleRate,
      duration: pcmData.length / audioContext.sampleRate
    });
  }

  // Convert Int16 to Float32 with optimized processing
  // Using a more efficient approach with fewer calculations
  const factor = 1.0 / 32768;
  for (let i = 0; i < pcmData.length; i++) {
    // Simple normalization for better performance
    channelData[i] = pcmData[i] * factor;
  }

  // Store original PCM data for iPhone debugging analysis
  (audioBuffer as any).originalPCMData = pcmData;
  (audioBuffer as any).originalDataSize = pcmData.length * 2; // 2 bytes per Int16

  return audioBuffer;
}

// Reset audio playback state - completely stops all audio immediately
export function clearAudioQueue(): void {
  // Stop current audio source if it exists
  if (currentSource) {
    try {
      currentSource.stop();
      currentSource.disconnect();
    } catch (e) {
      // Ignore errors if already stopped
    }
    currentSource = null;
  }

  // Clear the audio queue
  audioQueue = [];

  // Reset playback state
  lastEndTime = 0;
  isPlaying = false;
  isAssistantSpeaking = false;

  // Audio queue cleared - microphone continues to work normally
  console.log('Audio queue cleared - microphone remains active');

  // Reset gain node to stop any ongoing audio
  if (gainNode && audioContext) {
    try {
      // Cancel any scheduled parameter changes
      gainNode.gain.cancelScheduledValues(audioContext.currentTime);
      // Set gain to 0 immediately to silence any ongoing audio
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      // Then ramp back up to the platform-specific gain value for future audio
      const safariConfig = getSafariConfig();
      gainNode.gain.linearRampToValueAtTime(safariConfig.OUTPUT_GAIN, audioContext.currentTime + 0.1);

      // Create a silent buffer and play it to flush any remaining audio
      try {
        const silentBuffer = audioContext.createBuffer(1, 1024, audioContext.sampleRate);
        const silentSource = audioContext.createBufferSource();
        silentSource.buffer = silentBuffer;
        silentSource.connect(audioContext.destination);
        silentSource.start();
        silentSource.stop(audioContext.currentTime + 0.1);
        console.log('Played silent buffer to flush audio pipeline');
      } catch (silentErr) {
        console.error('Error playing silent buffer:', silentErr);
      }
    } catch (e) {
      console.error('Error resetting gain node:', e);
    }
  }

  // Temporarily suspend the audio context to ensure all audio stops
  if (audioContext && audioContext.state === 'running') {
    try {
      audioContext.suspend().then(() => {
        // Resume it after a short delay if we're still recording
        if (isRecording) {
          setTimeout(() => {
            if (audioContext) {
              audioContext.resume().catch(err => {
                console.error('Error resuming audio context after suspension:', err);
              });
            }
          }, 100);
        }
      }).catch(err => {
        console.error('Error suspending audio context:', err);
      });
    } catch (suspendErr) {
      console.error('Error suspending audio context:', suspendErr);
    }
  }

  // Make sure we're still recording after clearing the queue
  // This ensures we can continue to capture user speech for interruption
  if (isRecording && audioContext) {
    try {
      // Ensure microphone connections are still active
      if (mediaStreamSource && scriptProcessor && !mediaStreamSource.numberOfOutputs) {
        if (inputGainNode) {
          // Reconnect with Safari gain compensation
          mediaStreamSource.connect(inputGainNode);
          inputGainNode.connect(scriptProcessor);
          scriptProcessor.connect(audioContext.destination);
          console.log('Reconnected audio input pipeline with Safari gain compensation after clearing audio');
        } else {
          // Fallback to direct connection
          mediaStreamSource.connect(scriptProcessor);
          scriptProcessor.connect(audioContext.destination);
          console.log('Reconnected audio input pipeline after clearing audio');
        }
      }
    } catch (err) {
      console.error('Error ensuring recording continues after clearing audio:', err);
    }
  }

  console.log('Audio playback completely reset - all voice stopped');
}

// Close all connections and clean up resources
export function closeConnection(): void {
  console.log('Closing all connections and cleaning up resources');

  // Send voice logs before cleanup
  try {
    import('./voiceLogging').then(({ sendVoiceLogsBatch }) => {
      sendVoiceLogsBatch('websocket_cleanup').catch(err => {
        console.log('Error sending voice logs during cleanup:', err);
      });
    }).catch(err => {
      console.log('Error importing voice logging during cleanup:', err);
    });
  } catch (err) {
    console.log('Error with voice logging during cleanup:', err);
  }

  // Stop recording if active
  if (isRecording) {
    stopRecording();
  }

  // Clear audio queue
  clearAudioQueue();

  // Clear ping interval
  if (pingInterval) {
    clearInterval(pingInterval);
    pingInterval = null;
    console.log('Cleared keepalive ping interval');
  }

  // Close WebSocket connection first
  if (socket) {
    try {
      console.log('Closing WebSocket connection');
      if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        socket.close(1000, 'Connection closed by user');
      }
      socket = null;
      isSocketConnected = false;
    } catch (wsErr) {
      console.error('Error closing WebSocket:', wsErr);
    }
  }

  // Stop all audio tracks
  if (mediaStream) {
    try {
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
    } catch (streamErr) {
      console.error('Error stopping media stream:', streamErr);
    }
  }

  // Clean up audio connections safely
  try {
    // Disconnect the audio chain in proper order
    safelyDisconnectAudioNode(mediaStreamSource, 'mediaStreamSource');
    safelyDisconnectAudioNode(inputGainNode, 'inputGainNode');
    safelyDisconnectAudioNode(scriptProcessor, 'scriptProcessor');

    // Clean up references
    scriptProcessor = null;
    mediaStreamSource = null;
  } catch (err) {
    console.log('Error during audio cleanup (expected during shutdown):', err);
  }

  // Close audio context
  if (audioContext) {
    try {
      audioContext.close();
      audioContext = null;
      gainNode = null;
      inputGainNode = null;
    } catch (error) {
      console.error('Error closing audio context:', error);
    }
  }

  // Reset state variables
  isRecording = false;
  isPlaying = false;
  lastEndTime = 0;
  isSocketConnected = false;

  // Audio state reset - microphone enabled by default

  // Reset WebSocket safety variables if not unmounting
  // (keep the unmounting flag if we're in the process of unmounting)
  if (!isUnmounting) {
    isUnmounting = false;
  }

  console.log('All connections closed and resources cleaned up');
}

// Force reconnect WebSocket - useful for manual reconnection
export function forceReconnect(): void {
  console.log('Forcing WebSocket reconnection');

  // Close current connection if it exists
  if (socket) {
    try {
      if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        socket.close(1000, 'Force reconnect requested');
      }
      socket = null;
      isSocketConnected = false;
    } catch (err) {
      console.error('Error closing WebSocket during force reconnect:', err);
    }
  }

  // Reset connection state
  isSocketConnected = false;

  // Clear any existing audio state
  clearAudioQueue();

  // Connection reset - microphone enabled by default

  console.log('WebSocket connection reset - ready for new connection');
}

// Reset all connection state for fresh reconnection
export function resetConnectionState(isReconnection: boolean = false): void {
  console.log(`Resetting all connection state for ${isReconnection ? 'reconnection' : 'fresh connection'}`);

  // Set reconnection mode flag
  isReconnectionMode = isReconnection;

  // Reset WebSocket state
  isSocketConnected = false;
  socket = null;

  // Reset audio state
  isRecording = false;
  isPlaying = false;
  lastEndTime = 0;

  // Connection state reset - microphone enabled by default for all connections
  console.log('Connection state reset - microphone will be enabled by default');

  // Reset unmounting flag to allow new operations
  isUnmounting = false;

  // Clear any queued audio
  clearAudioQueue();

  console.log('Connection state reset completed');
}

// Clear reconnection mode flag
export function clearReconnectionMode(): void {
  isReconnectionMode = false;
  console.log('Reconnection mode cleared');
}

// Helper function to safely disconnect audio nodes
function safelyDisconnectAudioNode(node: AudioNode | null, nodeName: string): void {
  if (!node) return;

  try {
    // Check if the node has any outputs before trying to disconnect
    if ('numberOfOutputs' in node && node.numberOfOutputs > 0) {
      node.disconnect();
    }
  } catch (err) {
    console.log(`Error disconnecting ${nodeName} (expected during cleanup):`, err);
  }
}

// Add this function to set the WebSocket
export function setWebSocket(ws: WebSocket): void {
  // Check if component is unmounting
  if (isUnmounting) {
    console.log('WebSocket safety: Component is unmounting, skipping WebSocket setup');
    return;
  }

  // Check if the WebSocket is valid
  if (!ws) {
    console.error('Invalid WebSocket provided to setWebSocket');
    return;
  }

  // Check the WebSocket state
  if (ws.readyState !== WebSocket.OPEN && ws.readyState !== WebSocket.CONNECTING) {
    console.error(`WebSocket in invalid state: ${
      ws.readyState === WebSocket.CLOSING ? 'CLOSING' :
      ws.readyState === WebSocket.CLOSED ? 'CLOSED' : 'UNKNOWN'
    }`);
    return;
  }

  // Remove any existing event listeners from the previous socket
  if (socket) {
    try {
      // Use a clone of the socket to avoid modifying while iterating
      const oldSocket = socket;
      // Clear the reference first to prevent any new operations
      socket = null;

      // Try to remove event listeners (this may fail if the socket is already closed)
      try {
        oldSocket.removeEventListener('open', () => {});
        oldSocket.removeEventListener('close', () => {});
        oldSocket.removeEventListener('error', () => {});
        oldSocket.removeEventListener('message', () => {});
      } catch (err) {
        console.log('Error removing event listeners from old socket:', err);
      }
    } catch (err) {
      console.error('Error cleaning up previous WebSocket:', err);
    }
  }

  // Set the new socket
  socket = ws;
  console.log('New WebSocket set in audioStreamingStatic');

  // Add event listeners to track connection state and messages
  socket.addEventListener('open', () => {
    // Check if component is unmounting
    if (isUnmounting) {
      console.log('WebSocket safety: Component unmounted during WebSocket open, closing connection');
      try {
        socket?.close(1000, 'Component unmounted');
      } catch (err) {
        console.error('Error closing WebSocket after unmount:', err);
      }
      return;
    }

    console.log('WebSocket connected in audioStreamingStatic');
    isSocketConnected = true;

    // Send a test ping to verify the connection
    try {
      socket?.send(JSON.stringify({
        type: "ping",
        timestamp: Date.now()
      }));
      console.log('Sent test ping after WebSocket connection');
    } catch (err) {
      console.error('Error sending test ping:', err);
    }

    // Start periodic ping to prevent keepalive timeout
    if (pingInterval) {
      clearInterval(pingInterval);
    }
    pingInterval = setInterval(() => {
      if (socket && socket.readyState === WebSocket.OPEN && !isUnmounting) {
        try {
          socket.send(JSON.stringify({
            type: "ping",
            timestamp: Date.now()
          }));
          console.log('Sent keepalive ping');
        } catch (err) {
          console.error('Error sending keepalive ping:', err);
          if (pingInterval) {
            clearInterval(pingInterval);
            pingInterval = null;
          }
        }
      }
    }, 30000); // Send ping every 30 seconds
  });

  socket.addEventListener('close', (event) => {
    isSocketConnected = false;

    // Log specific information for common close codes
    if (event.code === 1008) {
      console.error('Policy Violation: The connection was terminated because a message was received that violates the endpoint\'s policy.');
    }
  });

  socket.addEventListener('error', (error) => {
    // Check if component is unmounting
    if (isUnmounting) {
      // Component unmounted during WebSocket error, ignoring
      return;
    }

    console.error('WebSocket error in audioStreamingStatic:', error);
  });

  socket.addEventListener('message', (event) => {
    // Check if component is unmounting
    if (isUnmounting) {
      // Component unmounted during WebSocket message, ignoring
      return;
    }

    try {
      const data = JSON.parse(event.data);

      // Track the last message type for context
      lastMessageType = data.type;

      // Handle ping/pong for keepalive
      if (data.type === 'ping') {
        // Respond to server ping with pong
        try {
          socket?.send(JSON.stringify({
            type: 'pong',
            timestamp: Date.now()
          }));
        } catch (err) {
          console.error('Error sending pong response:', err);
        }
        return; // Don't process ping messages further
      }

      if (data.type === 'pong') {
        // Server responded to our ping - connection is alive
        console.log('Received pong from server - connection alive');
        return; // Don't process pong messages further
      }

      // Optimized audio timestamp tracking
      if (data.type === 'response.audio.delta' && data.delta) {
        lastReceivedAudioTimestamp = Date.now();
      }

      // WebSocket message received - microphone continues to work normally

      // Check for any interruption indicators
      if (INTERRUPTION_INDICATORS.includes(data.type)) {
        // Interruption detected

        // If the assistant is currently speaking, stop all audio
        if (isAssistantSpeaking) {
          // Assistant was speaking - stopping all audio immediately
          clearAudioQueue();
        }
      }

      // Check for pause message from server
      if (data.type === 'pause_conversation' || data.type === 'pause_acknowledged') {
        // Pause message received from server - stopping all audio immediately
        clearAudioQueue();
      }

      // Also check for new assistant responses after user input
      if (data.type === "response.text.delta" &&
          lastMessageType === "conversation.item.input_audio_transcription.completed") {
        // New response after user input - clearing previous audio
        clearAudioQueue();
      }
    } catch (e) {
      console.warn('Error parsing WebSocket message:', e);
    }
  });

  // WebSocket set in audioStreamingStatic
}

// Start recording audio
export async function startRecording(): Promise<void> {
  // Check if component is unmounting
  if (isUnmounting) {
    console.log('WebSocket safety: Component is unmounting, skipping recording start');
    throw new Error('Component unmounting');
  }

  if (isRecording) {
    console.log('Already recording');
    return;
  }



  // More detailed WebSocket connection check
  if (!socket) {
    console.error('WebSocket is null or undefined');
    throw new Error('WebSocket is not initialized');
  }

  if (socket.readyState !== WebSocket.OPEN) {
    console.error('WebSocket is not in OPEN state, current state:', socket.readyState);
    throw new Error('WebSocket not connected');
  }

  try {
    console.log('Starting recording with enhanced VAD...');
    const safariConfig = getSafariConfig();
    const isSafari = getIsSafari();

    mediaStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        channelCount: 1,          // Mono audio
        sampleRate: safariConfig.INPUT_SAMPLE_RATE,  // Use Safari-optimized input sample rate
        sampleSize: 16,           // 16-bit PCM
        echoCancellation: true,
        noiseSuppression: true,   // Enable noise suppression globally to reduce background noise
        autoGainControl: false    // Keep auto gain control disabled to preserve manual volume control
      }
    });

    // Check if component unmounted during getUserMedia
    if (isUnmounting) {
      console.log('WebSocket safety: Component unmounted during getUserMedia, aborting recording start');
      // Clean up the media stream
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
      throw new Error('Component unmounted during getUserMedia');
    }

    // Initialize audio context with user gesture flag set to true
    if (!audioContext) {
      await initAudioContext(true);
    } else if (audioContext.state === 'suspended') {
      // Resume the audio context if it's suspended
      await audioContext.resume();
      console.log('Audio context resumed before recording');
    }

    // Check if component unmounted during audio context initialization
    if (isUnmounting) {
      console.log('WebSocket safety: Component unmounted during audio context initialization, aborting recording start');
      // Clean up the media stream
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        mediaStream = null;
      }
      throw new Error('Component unmounted during audio context initialization');
    }

    if (!audioContext) {
      throw new Error('Failed to initialize audio context');
    }

    mediaStreamSource = audioContext.createMediaStreamSource(mediaStream);

    // Create a ScriptProcessor for audio processing
    const processorBufferSize = 2048;
    scriptProcessor = audioContext.createScriptProcessor(processorBufferSize, 1, 1);

    scriptProcessor.onaudioprocess = (audioProcessingEvent) => {
      // Check if component is unmounting or not recording
      if (isUnmounting || !isRecording) return;

      const inputBuffer = audioProcessingEvent.inputBuffer;
      const inputData = inputBuffer.getChannelData(0);

      // Apply Safari-specific input gain compensation
      const safariConfig = getSafariConfig();
      const isSafari = getIsSafari();
      if (isSafari && safariConfig.INPUT_GAIN !== 1.0) {
        for (let i = 0; i < inputData.length; i++) {
          inputData[i] *= safariConfig.INPUT_GAIN;
          // Prevent clipping
          inputData[i] = Math.max(-1, Math.min(1, inputData[i]));
        }
      }

      // Convert Float32Array to Int16Array
      const int16Data = new Int16Array(inputData.length);
      for (let i = 0; i < inputData.length; i++) {
        // Convert float32 to int16
        const s = Math.max(-1, Math.min(1, inputData[i]));
        int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
      }

      // Convert to base64
      const base64Audio = int16ArrayToBase64(int16Data);

      // Send audio data to server
      if (socket && socket.readyState === WebSocket.OPEN && !isUnmounting) {
        socket.send(JSON.stringify({
          type: "audio",
          audio: base64Audio
        }));
        lastSentAudioTimestamp = Date.now();
      }
    };

    // Connect audio pipeline with Safari-specific gain compensation
    if (inputGainNode) {
      // Use the input gain node for Safari compensation
      mediaStreamSource.connect(inputGainNode);
      inputGainNode.connect(scriptProcessor);
      scriptProcessor.connect(audioContext.destination);
      console.log('Audio pipeline connected with Safari input gain compensation');
    } else {
      // Fallback to direct connection
      mediaStreamSource.connect(scriptProcessor);
      scriptProcessor.connect(audioContext.destination);
      console.log('Audio pipeline connected without gain compensation');
    }

    // Configure VAD settings if using AudioWorklet
    if (audioWorkletNode) {
      audioWorkletNode.port.postMessage({
        type: 'vadConfig',
        config: {
          voiceTrigger: 0.05,      // Increased threshold to reduce noise sensitivity
          voiceStop: 0.03,         // Increased stop threshold to reduce noise pickup
          smoothingTimeConstant: 0.95,
          minVoiceFrames: 8,       // Require more consecutive voice frames to reduce false positives
          energyThreshold: 0.04,   // Increased energy threshold to filter out background noise
          bufferSize: 1024         // Larger buffer for better analysis
        }
      });
    }

    // Set recording state
    isRecording = true;
    console.log('Recording started successfully with interruption support');

    audioContext.resume();
  } catch (error) {
    // Check if component unmounted during recording setup
    if (isUnmounting) {
      console.log('WebSocket safety: Component unmounted during recording setup, ignoring error');
      // Clean up any resources
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        mediaStream = null;
      }
      // Clean up audio nodes safely
      safelyDisconnectAudioNode(mediaStreamSource, 'mediaStreamSource during error cleanup');
      safelyDisconnectAudioNode(scriptProcessor, 'scriptProcessor during error cleanup');

      mediaStreamSource = null;
      scriptProcessor = null;
      throw new Error('Component unmounted during recording setup');
    }

    console.error("Error initializing microphone:", error);
    throw error;
  }
}

// Helper function to convert Int16Array to base64
function int16ArrayToBase64(int16Array: Int16Array): string {
  const uint8Array = new Uint8Array(int16Array.buffer);
  let binaryString = '';
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  return btoa(binaryString);
}

// Stop recording audio
export function stopRecording(): void {
  if (!isRecording) {
    return;
  }

  console.log('WebSocket safety: Stopping recording' + (isUnmounting ? ' during unmount' : ''));
  isRecording = false;

  // Safely disconnect audio nodes
  try {
    // Disconnect AudioWorkletNode if it exists
    if (audioWorkletNode) {
      safelyDisconnectAudioNode(audioWorkletNode, 'audioWorkletNode');
      audioWorkletNode = null;
    }

    // Disconnect the audio chain
    safelyDisconnectAudioNode(mediaStreamSource, 'mediaStreamSource');
    safelyDisconnectAudioNode(inputGainNode, 'inputGainNode');
    safelyDisconnectAudioNode(scriptProcessor, 'scriptProcessor');

    scriptProcessor = null;
  } catch (error) {
    console.log('Error during audio node cleanup (expected during shutdown):', error);
  }

  // Stop all audio tracks
  if (mediaStream) {
    try {
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
    } catch (streamErr) {
      console.error('Error stopping media stream tracks:', streamErr);
    }
  }

  mediaStreamSource = null;

  // Suspend the audio context if it's active
  if (audioContext && audioContext.state === 'running') {
    try {
      audioContext.suspend().catch(err => {
        console.error('Error suspending audio context:', err);
      });
    } catch (contextErr) {
      console.error('Error suspending audio context:', contextErr);
    }
  }
}

/**
 * Set custom audio output volume (gain)
 * @param {number} volume - Volume multiplier (1.0 = normal, 2.0 = double volume, etc.)
 */
export function setAudioVolume(volume: number): void {
  if (gainNode && audioContext) {
    // Clamp volume between 0 and 5 to prevent damage
    const clampedVolume = Math.max(0, Math.min(5, volume));
    gainNode.gain.setValueAtTime(clampedVolume, audioContext.currentTime);
    console.log(`Audio output volume set to: ${clampedVolume}x`);
  } else {
    console.warn('Audio context not initialized. Cannot set volume.');
  }
}

/**
 * Get current audio output volume
 * @returns {number} Current volume multiplier
 */
export function getAudioVolume(): number {
  if (gainNode) {
    return gainNode.gain.value;
  }
  return 1.0;
}

/**
 * Emergency cleanup function for logout - stops all audio and WebSocket connections immediately
 * This function is safe to call from any logout process
 */
export function emergencyLogoutCleanup(): void {
  console.log('Emergency logout cleanup: Stopping all audio and WebSocket connections');

  // Emergency voice logging cleanup
  try {
    import('./voiceLogging').then(({ emergencyVoiceLoggingCleanup }) => {
      emergencyVoiceLoggingCleanup();
    }).catch(err => {
      console.log('Error with emergency voice logging cleanup:', err);
    });
  } catch (err) {
    console.log('Error importing voice logging during emergency cleanup:', err);
  }

  // Force release wake lock during emergency cleanup
  try {
    import('../utils/wakeLock').then(({ forceReleaseWakeLock }) => {
      forceReleaseWakeLock('Emergency logout cleanup');
    }).catch(err => {
      console.log('Error importing wake lock during emergency cleanup:', err);
    });
  } catch (err) {
    console.log('Error releasing wake lock during emergency cleanup:', err);
  }

  try {
    // Set unmounting flag to prevent new operations
    setUnmountingFlag(true);

    // Stop any current audio source immediately
    if (currentSource) {
      try {
        currentSource.stop();
        currentSource.disconnect();
      } catch (e) {
        // Ignore errors if already stopped
      }
      currentSource = null;
    }

    // Clear audio queue
    audioQueue = [];

    // Mute audio immediately
    if (gainNode && audioContext) {
      try {
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      } catch (e) {
        console.log('Error muting audio during logout cleanup:', e);
      }
    }

    // Close all connections
    closeConnection();

    console.log('Emergency logout cleanup completed');
  } catch (error) {
    console.log('Error during emergency logout cleanup (continuing):', error);
  }
}

/**
 * Get mobile/Safari-specific audio configuration for debugging
 * @returns {object} Mobile/Safari audio configuration details
 */
export function getSafariAudioConfig() {
  const safariConfig = getSafariConfig();
  return {
    isSafari: getIsSafari(),
    isIOS: getIsIOS(),
    isMobile: getIsMobile(),
    config: safariConfig,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server-side rendering'
  };
}

/**
 * Initialize global logout cleanup listener
 * This ensures audio is stopped even if logout doesn't call our cleanup functions
 */
function initializeGlobalCleanupListeners(): void {
  if (typeof window === 'undefined') return;

  // Add beforeunload listener to clean up audio on page unload/logout
  const handleBeforeUnload = () => {
    try {
      emergencyLogoutCleanup();
    } catch (e) {
      console.log('Error during beforeunload cleanup:', e);
    }
  };

  // Add pagehide listener for mobile browsers
  const handlePageHide = () => {
    try {
      emergencyLogoutCleanup();
    } catch (e) {
      console.log('Error during pagehide cleanup:', e);
    }
  };

  window.addEventListener('beforeunload', handleBeforeUnload);
  window.addEventListener('pagehide', handlePageHide);

  console.log('Global audio cleanup listeners initialized');
}

// Initialize cleanup listeners when module loads
if (typeof window !== 'undefined') {
  initializeGlobalCleanupListeners();
}

/**
 * Check if the browser supports the required audio APIs
 * @returns {boolean} True if the browser is compatible
 */
export function checkBrowserCompatibility(): boolean {
  // Only run in browser environment
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false; // Not compatible during SSR
  }

  // Check for AudioContext support
  const hasAudioContext = typeof (window.AudioContext || (window as any).webkitAudioContext) !== 'undefined';

  // Check for getUserMedia support
  const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

  // Check for WebSocket support
  const hasWebSocket = typeof WebSocket !== 'undefined';

  const isCompatible = hasAudioContext && hasGetUserMedia && hasWebSocket;

  if (!isCompatible) {
    console.error('Browser compatibility check failed:',
      { hasAudioContext, hasGetUserMedia, hasWebSocket });

    // Log specific missing features
    if (!hasAudioContext) console.error('AudioContext not supported');
    if (!hasGetUserMedia) console.error('getUserMedia not supported');
    if (!hasWebSocket) console.error('WebSocket not supported');
  }

  // Log mobile/Safari-specific configuration for debugging
  if (getIsSafari() || getIsMobile()) {
    console.log('Mobile/Safari detected - applying audio volume compensation:', getSafariAudioConfig());
  }

  return isCompatible;
}

/**
 * Diagnose WebSocket connection issues
 * @returns {object} Diagnostic information
 */
export function diagnoseConnection(): {
  isSocketConnected: boolean;
  socketState: string;
  lastSentAudio: number;
  lastReceivedAudio: number;
  timeSinceLastSent: number;
  timeSinceLastReceived: number;
  isRecording: boolean;
  audioContextState: string;
} {
  const now = Date.now();

  let socketState = 'not_initialized';
  if (socket) {
    switch (socket.readyState) {
      case WebSocket.CONNECTING: socketState = 'connecting'; break;
      case WebSocket.OPEN: socketState = 'open'; break;
      case WebSocket.CLOSING: socketState = 'closing'; break;
      case WebSocket.CLOSED: socketState = 'closed'; break;
    }
  }

  return {
    isSocketConnected: isSocketConnected,
    socketState: socketState,
    lastSentAudio: lastSentAudioTimestamp,
    lastReceivedAudio: lastReceivedAudioTimestamp,
    timeSinceLastSent: lastSentAudioTimestamp ? now - lastSentAudioTimestamp : -1,
    timeSinceLastReceived: lastReceivedAudioTimestamp ? now - lastReceivedAudioTimestamp : -1,
    isRecording: isRecording,
    audioContextState: audioContext ? audioContext.state : 'not_initialized'
  };
}

// Add a ping function to test the connection
export function pingServer(): boolean {
  if (!socket || socket.readyState !== WebSocket.OPEN) {
    // Cannot ping: WebSocket not open
    return false;
  }

  try {
    socket.send(JSON.stringify({
      type: "ping",
      timestamp: Date.now()
    }));
    // Ping sent to server
    return true;
  } catch (error) {
    console.error('Error sending ping:', error);
    return false;
  }
}

// Get the current microphone stream
export function getMicrophoneStream(): MediaStream | null {
  return mediaStream;
}

// Check if the assistant is currently speaking
export function getAssistantSpeakingState(): boolean {
  return isAssistantSpeaking;
}



// Simple mute function - only stops sending audio data, doesn't tear down pipeline
export function muteMicrophone(): void {
  if (!isRecording) {
    console.log('Microphone already muted');
    return;
  }

  console.log('Muting microphone (stopping audio transmission)');
  isRecording = false;

  // Note: We keep the audio context, media stream, and processors running
  // This prevents interference with audio playback
}

// Simple unmute function - only resumes sending audio data, doesn't rebuild pipeline
export function unmuteMicrophone(): void {
  if (isRecording) {
    console.log('Microphone already unmuted');
    return;
  }

  // Check if we have the necessary components
  if (!mediaStream || !mediaStreamSource || !scriptProcessor) {
    console.log('Audio pipeline not initialized, cannot unmute. Use startRecording() first.');
    return;
  }

  // Check if WebSocket is connected
  if (!socket || socket.readyState !== WebSocket.OPEN) {
    console.log('WebSocket not connected, cannot unmute microphone');
    return;
  }



  console.log('Unmuting microphone (resuming audio transmission)');
  isRecording = true;

  // Ensure audio context is running
  if (audioContext && audioContext.state === 'suspended') {
    audioContext.resume().catch(err => {
      console.error('Error resuming audio context:', err);
    });
  }
}

/**
 * WebSocket Safety Functions
 * These functions help prevent race conditions when components are unmounting
 */

// Set the unmounting flag - optimized with minimal logging
export function setUnmountingFlag(value: boolean): void {
  isUnmounting = value;
}

// Check if component is unmounting
export function isComponentUnmounting(): boolean {
  return isUnmounting;
}

// Safe WebSocket operation wrapper - optimized for performance
export async function safeWebSocketOperation<T>(
  operation: () => Promise<T>,
  componentName: string,
  operationName: string
): Promise<T> {
  // Fast check if component is unmounting
  if (isUnmounting) {
    throw new Error('Component unmounting');
  }

  try {
    return await operation();
  } catch (err) {
    // Fast check if component unmounted during operation
    if (isUnmounting) {
      throw new Error('Component unmounted during operation');
    }

    console.error(`${componentName}: Error during ${operationName}:`, err);
    throw err;
  }
}
