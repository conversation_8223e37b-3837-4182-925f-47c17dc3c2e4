// Common Feedback Service for handling feedback-related API requests
import { API_BASE_URL } from '../config/appConfig';

const BASE_URL = API_BASE_URL;

// Types for Common Feedback API
export interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  source: 'voice' | 'ai';
  is_placeholder: boolean;
}

export interface ConversationSession {
  messages: ConversationMessage[];
  started_at: string;
  ended_at: string;
  metadata: {
    session_id: string;
    language: string;
    voice_enabled: boolean;
  };
  tokens_used: number;
}

export interface ConversationData {
  agent: {
    name: string;
    role: string;
  };
  sessions: ConversationSession[];
  language: string;
}

export interface FeedbackSubmissionRequest {
  conversation_data: ConversationData;
}

export interface FeedbackSubmissionResponse {
  id: number;
  user_id: number;
  conversation_data: ConversationData;
  created_at: string;
}

export interface FeedbackListResponse {
  feedback: FeedbackSubmissionResponse[];
  total: number;
}

// Common Feedback Service Class
export class CommonFeedbackService {
  private apiBaseUrl: string;
  private authToken: string;

  constructor(apiBaseUrl: string = BASE_URL, authToken: string = '') {
    this.apiBaseUrl = apiBaseUrl;
    this.authToken = authToken;
  }

  // Update auth token
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  // Submit feedback to the API
  async submitFeedback(conversationData: ConversationData): Promise<FeedbackSubmissionResponse> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/common-feedback/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`
        },
        body: JSON.stringify({ conversation_data: conversationData })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`Failed to submit feedback: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  // Get user's feedback history
  async getUserFeedback(limit: number = 30): Promise<FeedbackListResponse> {
    try {
      const response = await fetch(
        `${this.apiBaseUrl}/common-feedback/my-feedback?limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: response.statusText }));
        throw new Error(`Failed to get feedback: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting feedback:', error);
      throw error;
    }
  }

  // Helper method to create conversation data structure
  static createConversationData(
    messages: ConversationMessage[],
    sessionId: string,
    language: string = 'en'
  ): ConversationData {
    const now = new Date().toISOString();
    const startTime = messages.length > 0 ? messages[0].timestamp : now;
    const endTime = messages.length > 0 ? messages[messages.length - 1].timestamp : now;

    return {
      agent: {
        name: "Common Feedback Assistant",
        role: "cm_fa"
      },
      sessions: [
        {
          messages,
          started_at: startTime,
          ended_at: endTime,
          metadata: {
            session_id: sessionId,
            language,
            voice_enabled: true
          },
          tokens_used: messages.reduce((total, msg) => total + msg.content.length, 0)
        }
      ],
      language
    };
  }

  // Helper method to create a message
  static createMessage(
    role: 'user' | 'assistant',
    content: string,
    source: 'voice' | 'ai' = 'voice',
    isPlaceholder: boolean = false
  ): ConversationMessage {
    return {
      role,
      content,
      timestamp: new Date().toISOString(),
      source,
      is_placeholder: isPlaceholder
    };
  }
}

// Create a singleton instance
export const commonFeedbackService = new CommonFeedbackService();

// Export default instance
export default commonFeedbackService;
